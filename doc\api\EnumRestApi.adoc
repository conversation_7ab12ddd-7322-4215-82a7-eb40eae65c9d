:restdocDir: ../../lib/backend-data/build/generated-snippets
= Enum-API

REST-API for managing configurable enumerations in the pet clinic application.
It provides operations to create, update, and retrieve enumeration values organized by discriminator (art).
Each enumeration type is identified by a discriminator and contains ordered items with unique codes, names, and display texts.

== Model

The main entity of an _Enum_ managed by this controller for persistence.
Each enum represents a configurable enumeration value with a discriminator for grouping.

.Enum entity
[source,java,options="nowrap"]
----
include::../../lib/backend-api/src/main/java/esy/api/info/Enum.java[indent=0,tags=properties]
----

**Key Properties:**
- `art`: Discriminator that groups related enumeration values (e.g., "SPECIES", "SKILL")
- `code`: Unique numeric identifier within the discriminator group
- `name`: Unique string identifier within the discriminator group
- `text`: Human-readable display text for the enumeration value

**Constraints:**
- All fields are required (not empty)
- Code must be positive or zero
- Combination of art + code should be unique
- Combination of art + name should be unique

A simplified representation of an _Enum_ for item selection purposes.

.EnumItem
[source,java,options="nowrap"]
----
include::../../lib/backend-api/src/main/java/esy/api/info/EnumItem.java[indent=0,tags=properties]
----

== Operations

=== `POST /api/enum/{art}`

This operation creates a new _Enum_ entity for a specific discriminator.

****

.CURL
include::{restdocDir}/post-api-enum/curl-request.adoc[]

.Request
include::{restdocDir}/post-api-enum/http-request.adoc[]

.Response
include::{restdocDir}/post-api-enum/response-body.adoc[]

****

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the object already exists.

=== `PUT /api/enum/{art}/{code}`

This operation updates an existing _Enum_ entity for a specific discriminator and code.

****

.CURL
include::{restdocDir}/put-api-enum/curl-request.adoc[]

.Request
include::{restdocDir}/put-api-enum/http-request.adoc[]

.Response
include::{restdocDir}/put-api-enum/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Not Found` or code 404 if the enum does not exist.

This operation reports `Bad Request` or code 400 if the data was not processed.

=== `GET /api/enum/{art}`

This operation returns all persisted _Enum_ entities for a specific discriminator as EnumItem collection.
Results are ordered by art and code.

****

.CURL
include::{restdocDir}/get-api-enum/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-enum/http-request.adoc[]

.Response
include::{restdocDir}/get-api-enum/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.
The result can be empty if no enums exist for the discriminator.

== Standard Spring Data REST Endpoints

In addition to the custom endpoints above, the Enum entity also supports standard Spring Data REST operations:

=== `GET /api/enum`

Returns all persisted _Enum_ entities with pagination and HATEOAS links.
Supports query parameters for filtering and sorting.

=== `GET /api/enum/{id}`

Returns a single _Enum_ entity by its UUID.

=== `POST /api/enum`

Creates a new _Enum_ entity (alternative to the custom POST endpoint).

=== `PUT /api/enum/{id}`

Updates or creates an _Enum_ entity by its UUID.

=== `PATCH /api/enum/{id}`

Partially updates an _Enum_ entity by its UUID.

=== `DELETE /api/enum/{id}`

Deletes an _Enum_ entity by its UUID.

== Query Parameters

The standard Spring Data REST endpoints support advanced query parameters:

=== String Field Filtering
* **Contains**: `?name=Alpha` - Find enums with name containing "Alpha" (case-insensitive)
* **Like pattern**: `?name=Alpha%` - Find enums with name starting with "Alpha" (case-insensitive)
* **Art filtering**: `?art=SPECIES` - Find enums with specific discriminator

=== Code Field Filtering
* **Exact match**: `?code=1` - Find enums with specific code
* **Range**: `?code=1&code=5` - Find enums with code between 1 and 5 (inclusive)
* **Multiple values**: `?code=1&code=3&code=5` - Find enums with specific codes

=== Sorting and Pagination
* **Sorting**: Use `sort` parameter (e.g., `?sort=code,asc` or `?sort=name,desc`)
* **Pagination**: Supports `page` and `size` parameters

=== Example Queries
* `GET /api/enum?art=SPECIES&sort=code,asc` - All species enums ordered by code
* `GET /api/enum?name=Dog&art=SPECIES` - Find "Dog" species enum
* `GET /api/enum?code=1&code=3` - Find enums with code 1 or 3
